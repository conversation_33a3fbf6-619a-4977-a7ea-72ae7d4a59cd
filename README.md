# CalculatorSuites

A static website featuring various calculators for tax, discount, investment, mortgage/loan, and health calculations.

## Overview

CalculatorSuites is a collection of free online calculators designed to help users with various financial, tax, and health-related calculations. The website is built using vanilla HTML, CSS, and JavaScript without any frameworks or server-side dependencies, making it easy to deploy on any static hosting service.

## Features

- **Tax Calculators**: GST Calculator, Income Tax Calculator, Tax Comparison Tool
- **Discount Calculators**: Percentage Discount, Amount-based Discount, Bulk Discount
- **Investment Calculators**: SIP Calculator, Compound Interest, Lump Sum Investment, Investment Goal
- **Mortgage/Loan Calculators**: EMI Calculator, Loan Affordability, Loan Comparison, Amortization Schedule
- **Health Calculators**: BMI Calculator, Calorie Calculator, Pregnancy Due Date, Body Fat Percentage

## Technical Details

- Built with vanilla HTML5, CSS3, and JavaScript
- No frameworks or libraries required
- Responsive design for all device sizes
- Client-side form validation
- LocalStorage for saving user preferences
- SEO optimized with structured data
- Google AdSense integration

## Project Structure

```
calculatorsuites/
├── index.html                  # Homepage
├── assets/
│   ├── css/
│   │   ├── main.css            # Global styles
│   │   ├── calculator.css      # Calculator-specific styles
│   │   └── responsive.css      # Media queries
│   ├── js/
│   │   ├── main.js             # Global functionality
│   │   ├── utils.js            # Shared utility functions
│   │   ├── storage.js          # LocalStorage management
│   │   ├── contact.js          # Contact form handling
│   │   ├── resource-optimizer.js # Resource optimization
│   │   ├── structured-data.js  # SEO schema markup
│   │   ├── search-intent-enhancements.js # SEO enhancements
│   │   └── calculators/        # Calculator-specific scripts
│   │       ├── tax.js
│   │       ├── discount.js
│   │       ├── investment.js
│   │       ├── mortgage.js
│   │       ├── health.js
│   │       ├── car-loan.js
│   │       └── bike-loan.js
│   └── images/                 # Images and icons
├── tax/                        # Tax calculator pages
├── discount/                   # Discount calculator pages
├── investment/                 # Investment calculator pages
├── mortgage/                   # Mortgage/Loan calculator pages
├── health/                     # Health calculator pages
├── robots.txt                  # Robots.txt file
├── sitemap.xml                 # XML sitemap
├── .htaccess                   # Apache configuration
└── 404.html                    # 404 error page
```

## Deployment

1. Clone the repository
2. Customize the calculator formulas and UI as needed
3. Deploy to any static hosting service (GitHub Pages, Netlify, Vercel, etc.)

## SEO Optimization

- Each page includes proper meta tags, Open Graph tags, and Twitter Card tags
- Structured data using schema.org markup
- XML sitemap for search engine indexing
- Proper heading structure and semantic HTML
- Mobile-friendly design (responsive)
- Fast loading times with optimized assets

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

[Your Name]

## Acknowledgments

- Icons from [SVG Repo](https://www.svgrepo.com/)
- Fonts from [Google Fonts](https://fonts.google.com/)
